#include "stm32f10x.h"                  // Device header
void Buzzer_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	GPIO_InitTypeDef GPIO_Initstructure;
	GPIO_Initstructure.GPIO_Mode=GPIO_Mode_Out_PP;
	GPIO_Initstructure.GPIO_Pin=GPIO_Pin_10;
	GPIO_Initstructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_Initstructure);
	GPIO_SetBits(GPIOB,GPIO_Pin_10);
}
void Buzzer_ON(void)
{
	GPIO_ResetBits(GPIOB,GPIO_Pin_10);
}
void Buzzer_OFF(void)
{
	GPIO_SetBits(GPIOB,GPIO_Pin_10);
}
void Buzzer_Turn(void)
{
	if(GPIO_ReadOutputDataBit(GPIOB,GPIO_Pin_10)==0)
	{
		GPIO_SetBits(GPIOB,GPIO_Pin_10);
	}
	else
	{
		GPIO_ResetBits(GPIOB,GPIO_Pin_10);
	}
}
